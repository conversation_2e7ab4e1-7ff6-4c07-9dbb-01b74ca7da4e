<?php

namespace App\Http\Controllers;

use App\Events\BalanceUpdated;
use App\Events\DiceGameResult;
use App\Models\Currency;
use App\Services\DiceGameService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DiceGameController extends Controller
{
    protected DiceGameService $diceGameService;

    public function __construct(DiceGameService $diceGameService)
    {
        $this->diceGameService = $diceGameService;
    }

    /**
     * Process a dice roll.
     */
    public function roll(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'bet' => 'required|numeric|gt:0',
                'threshold' => 'required|numeric|min:2.00|max:98.00',
                'direction' => 'required|string|in:over,under',
                'currency_id' => 'sometimes|integer|exists:currencies,id',
            ]);

            $validated = $request->only(['bet', 'threshold', 'direction', 'currency_id']);

            $user = $request->user();

            // Get currency (default to first available if not specified)
            $currencyId = $validated['currency_id'] ?? Currency::first()->id;
            $currency = Currency::findOrFail($currencyId);

            // Process the dice roll
            $game = $this->diceGameService->processRoll(
                $user,
                $currency,
                $validated['bet'],
                $validated['threshold'],
                $validated['direction']
            );

            // Format the response
            $result = $this->diceGameService->formatGameResult($game);

            // Broadcast balance update to user's private channel
            broadcast(new BalanceUpdated(
                $user->id,
                $currency->code,
                $game->balance_after,
                number_format($game->balance_after / (10 ** $currency->decimals), $currency->decimals)
            ));

            // Broadcast dice game result to user's private channel
            broadcast(new DiceGameResult($user->id, $result));

            Log::info('Dice game processed', [
                'user_id' => $user->id,
                'game_id' => $game->id,
                'result' => $result,
            ]);

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);

        } catch (\InvalidArgumentException $e) {
            Log::warning('Invalid dice game request', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);

        } catch (\Exception $e) {
            Log::error('Dice game processing failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'An error occurred while processing your bet. Please try again.',
            ], 500);
        }
    }

    /**
     * Get win chance and multiplier for given parameters (for UI calculations).
     */
    public function calculate(Request $request)
    {
        try {
            $validated = $request->validate([
                'threshold' => 'required|numeric|min:2.00|max:98.00',
                'direction' => 'required|string|in:over,under',
            ]);

            $winChance = $this->diceGameService->calculateWinChance(
                $validated['threshold'],
                $validated['direction']
            );

            $multiplier = $this->diceGameService->calculateMultiplier($winChance);

            return response()->json([
                'success' => true,
                'data' => [
                    'win_chance' => $winChance,
                    'multiplier' => round($multiplier, 4),
                ],
            ]);

        } catch (\Exception) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid parameters',
            ], 400);
        }
    }

    /**
     * Get minimum bet information for a currency.
     */
    public function minimumBet(Request $request)
    {
        try {
            $validated = $request->validate([
                'currency_id' => 'required|integer|exists:currencies,id',
            ]);

            $currency = Currency::findOrFail($validated['currency_id']);
            $minimumBetInfo = $this->diceGameService->getMinimumBetInfo($currency);

            return response()->json([
                'success' => true,
                'data' => $minimumBetInfo,
            ]);

        } catch (\Exception) {
            return response()->json([
                'success' => false,
                'error' => 'Invalid parameters',
            ], 400);
        }
    }

    /**
     * Get game configuration (house edge, thresholds, etc.).
     */
    public function config()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'house_edge' => $this->diceGameService->getHouseEdge(),
                'min_threshold' => 2.00,
                'max_threshold' => 98.00,
                'min_roll' => 0.00,
                'max_roll' => 99.99,
            ],
        ]);
    }
}
