<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\DiceGame;
use App\Models\User;
use App\Services\BalanceService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DiceGameService
{
    protected BalanceService $balanceService;

    // House edge percentage (1% = 1.0)
    protected float $houseEdge = 1.0;

    public function __construct(BalanceService $balanceService)
    {
        $this->balanceService = $balanceService;
    }

    /**
     * Calculate win chance based on threshold and direction.
     */
    public function calculateWinChance(float $threshold, string $direction): float
    {
        if ($direction === 'over') {
            return 100.0 - $threshold;
        } else { // under
            return $threshold - 1.0;
        }
    }

    /**
     * Calculate multiplier based on win chance.
     */
    public function calculateMultiplier(float $winChance): float
    {
        if ($winChance <= 0) {
            return 0.0;
        }

        return (100.0 - $this->houseEdge) / $winChance;
    }

    /**
     * Calculate minimum bet to preserve house edge after rounding.
     * Formula: (1 / 10^decimals) / (house_edge / 100)
     */
    public function calculateMinimumBet(Currency $currency): float
    {
        $smallestUnit = 1 / (10 ** $currency->decimals);
        $houseEdgeFraction = $this->houseEdge / 100.0;

        return $smallestUnit / $houseEdgeFraction;
    }

    /**
     * Generate a random roll result between 0.00 and 99.99.
     */
    public function generateRoll(): float
    {
        // Generate random float between 0.00 and 99.99
        return round(mt_rand(0, 9999) / 100, 2);
    }

    /**
     * Determine if the roll is a win based on threshold and direction.
     */
    public function isWin(float $roll, float $threshold, string $direction): bool
    {
        if ($direction === 'over') {
            return $roll > $threshold;
        } else { // under
            return $roll < $threshold;
        }
    }

    /**
     * Validate bet parameters.
     */
    public function validateBet(User $user, Currency $currency, float $betAmount, float $threshold, string $direction): array
    {
        $errors = [];

        // Validate bet amount
        if ($betAmount <= 0) {
            $errors[] = 'Bet amount must be positive';
        }

        // Check minimum bet to preserve house edge
        $minimumBet = $this->calculateMinimumBet($currency);
        if ($betAmount < $minimumBet) {
            $errors[] = sprintf(
                'Minimum bet is %s %s to preserve house edge',
                number_format($minimumBet, $currency->decimals),
                $currency->code
            );
        }

        // Convert bet amount to minor units for balance check
        $betAmountMinor = (int) round($betAmount * (10 ** $currency->decimals));

        // Check user balance
        $userBalance = $this->balanceService->getBalance($user, $currency);
        if ($betAmountMinor > $userBalance) {
            $errors[] = 'Insufficient balance';
        }

        // Validate threshold
        if ($threshold < 1.00 || $threshold > 99.00) {
            $errors[] = 'Threshold must be between 1.00 and 99.00';
        }

        // Validate direction
        if (!in_array($direction, ['over', 'under'])) {
            $errors[] = 'Direction must be "over" or "under"';
        }

        // Validate win chance (must be positive)
        $winChance = $this->calculateWinChance($threshold, $direction);
        if ($winChance <= 0) {
            $errors[] = 'Invalid threshold for selected direction';
        }

        return $errors;
    }

    /**
     * Process a dice roll game.
     */
    public function processRoll(User $user, Currency $currency, float $betAmount, float $threshold, string $direction): DiceGame
    {
        // Validate the bet
        $errors = $this->validateBet($user, $currency, $betAmount, $threshold, $direction);
        if (!empty($errors)) {
            throw new \InvalidArgumentException(implode(', ', $errors));
        }

        return DB::transaction(function () use ($user, $currency, $betAmount, $threshold, $direction) {
            // Convert bet amount to minor units
            $betAmountMinor = (int) round($betAmount * (10 ** $currency->decimals));

            // Get current balance
            $balanceBefore = $this->balanceService->getBalance($user, $currency);

            // Calculate game parameters
            $winChance = $this->calculateWinChance($threshold, $direction);
            $multiplier = $this->calculateMultiplier($winChance);
            $roll = $this->generateRoll();
            $win = $this->isWin($roll, $threshold, $direction);

            // Calculate profit/loss
            if ($win) {
                $profit = (int) round($betAmountMinor * $multiplier) - $betAmountMinor;
            } else {
                $profit = -$betAmountMinor;
            }

            // Process the balance change
            $referenceId = DiceGame::generateReferenceId();

            if ($profit > 0) {
                // User won - credit the profit
                $this->balanceService->credit(
                    $user,
                    $currency,
                    $profit,
                    'dice_win',
                    $referenceId,
                    [
                        'game_type' => 'dice',
                        'bet_amount' => $betAmountMinor,
                        'threshold' => $threshold,
                        'direction' => $direction,
                        'roll' => $roll,
                        'multiplier' => $multiplier,
                    ]
                );
            } else {
                // User lost - debit the bet amount
                $this->balanceService->debit(
                    $user,
                    $currency,
                    abs($profit),
                    'dice_loss',
                    $referenceId,
                    [
                        'game_type' => 'dice',
                        'bet_amount' => $betAmountMinor,
                        'threshold' => $threshold,
                        'direction' => $direction,
                        'roll' => $roll,
                        'multiplier' => $multiplier,
                    ]
                );
            }

            // Get balance after transaction
            $balanceAfter = $this->balanceService->getBalance($user, $currency);

            // Create dice game record
            $diceGame = DiceGame::create([
                'user_id' => $user->id,
                'currency_id' => $currency->id,
                'bet_amount' => $betAmountMinor,
                'threshold' => $threshold,
                'direction' => $direction,
                'roll_result' => $roll,
                'win' => $win,
                'profit' => $profit,
                'multiplier' => $multiplier,
                'win_chance' => $winChance,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'reference_id' => $referenceId,
                'meta' => [
                    'house_edge' => $this->houseEdge,
                    'timestamp' => now()->timestamp,
                ],
                'created_at' => now(),
            ]);

            return $diceGame;
        });
    }

    /**
     * Get formatted game result for API response.
     */
    public function formatGameResult(DiceGame $game): array
    {
        $currency = $game->currency;
        $divisor = 10 ** $currency->decimals;

        return [
            'id' => $game->id,
            'roll' => $game->roll_result,
            'win' => $game->win,
            'profit' => $game->profit / $divisor,
            'profit_formatted' => number_format($game->profit / $divisor, $currency->decimals),
            'new_balance' => $game->balance_after / $divisor,
            'new_balance_formatted' => number_format($game->balance_after / $divisor, $currency->decimals),
            'multiplier' => $game->multiplier,
            'win_chance' => $game->win_chance,
            'bet_amount' => $game->bet_amount / $divisor,
            'bet_amount_formatted' => number_format($game->bet_amount / $divisor, $currency->decimals),
            'threshold' => $game->threshold,
            'direction' => $game->direction,
            'reference_id' => $game->reference_id,
            'created_at' => $game->created_at->toISOString(),
        ];
    }

    /**
     * Get minimum bet information for a currency.
     */
    public function getMinimumBetInfo(Currency $currency): array
    {
        $minimumBet = $this->calculateMinimumBet($currency);

        return [
            'minimum_bet' => $minimumBet,
            'minimum_bet_formatted' => number_format($minimumBet, $currency->decimals),
            'currency_code' => $currency->code,
            'currency_decimals' => $currency->decimals,
            'house_edge' => $this->houseEdge,
        ];
    }
}
