<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DiceGame extends Model
{
    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'currency_id',
        'bet_amount',
        'threshold',
        'direction',
        'roll_result',
        'win',
        'profit',
        'multiplier',
        'win_chance',
        'balance_before',
        'balance_after',
        'reference_id',
        'meta',
        'created_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'bet_amount' => 'integer',
        'threshold' => 'decimal:2',
        'roll_result' => 'decimal:2',
        'win' => 'boolean',
        'profit' => 'integer',
        'multiplier' => 'decimal:4',
        'win_chance' => 'decimal:2',
        'balance_before' => 'integer',
        'balance_after' => 'integer',
        'meta' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * Get the user that owns the dice game.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the currency for this dice game.
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Generate a unique reference ID for the game.
     */
    public static function generateReferenceId(): string
    {
        return 'dice_' . uniqid() . '_' . time();
    }
}
