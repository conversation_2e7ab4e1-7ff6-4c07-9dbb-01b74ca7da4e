<?php

namespace Tests\Unit;

use App\Models\Currency;
use App\Models\User;
use App\Services\BalanceService;
use App\Services\DiceGameService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DiceGameServiceTest extends TestCase
{
    use RefreshDatabase;

    protected DiceGameService $diceGameService;
    protected BalanceService $balanceService;
    protected User $user;
    protected Currency $currency;

    protected function setUp(): void
    {
        parent::setUp();

        $this->balanceService = new BalanceService();
        $this->diceGameService = new DiceGameService($this->balanceService);

        // Create test user and currency
        $this->user = User::factory()->create();
        $this->currency = Currency::create([
            'code' => 'USD',
            'decimals' => 2,
            'symbol' => '$',
        ]);

        // Initialize user balance
        $this->balanceService->startBalanceChain($this->user);
        $this->balanceService->credit($this->user, $this->currency, 10000, 'test_deposit'); // $100.00
    }

    public function test_calculate_win_chance_over(): void
    {
        $winChance = $this->diceGameService->calculateWinChance(50.0, 'over');
        $this->assertEquals(50.0, $winChance);

        $winChance = $this->diceGameService->calculateWinChance(75.0, 'over');
        $this->assertEquals(25.0, $winChance);

        $winChance = $this->diceGameService->calculateWinChance(1.0, 'over');
        $this->assertEquals(99.0, $winChance);
    }

    public function test_calculate_win_chance_under(): void
    {
        $winChance = $this->diceGameService->calculateWinChance(50.0, 'under');
        $this->assertEquals(49.0, $winChance);

        $winChance = $this->diceGameService->calculateWinChance(25.0, 'under');
        $this->assertEquals(24.0, $winChance);

        $winChance = $this->diceGameService->calculateWinChance(99.0, 'under');
        $this->assertEquals(98.0, $winChance);
    }

    public function test_calculate_multiplier(): void
    {
        // 50% win chance should give ~1.98x multiplier (99/50)
        $multiplier = $this->diceGameService->calculateMultiplier(50.0);
        $this->assertEquals(1.98, $multiplier);

        // 25% win chance should give ~3.96x multiplier (99/25)
        $multiplier = $this->diceGameService->calculateMultiplier(25.0);
        $this->assertEquals(3.96, $multiplier);

        // 10% win chance should give ~9.9x multiplier (99/10)
        $multiplier = $this->diceGameService->calculateMultiplier(10.0);
        $this->assertEquals(9.9, $multiplier);

        // 0% win chance should give 0 multiplier
        $multiplier = $this->diceGameService->calculateMultiplier(0.0);
        $this->assertEquals(0.0, $multiplier);
    }

    public function test_is_win_over(): void
    {
        $this->assertTrue($this->diceGameService->isWin(75.0, 50.0, 'over'));
        $this->assertFalse($this->diceGameService->isWin(25.0, 50.0, 'over'));
        $this->assertFalse($this->diceGameService->isWin(50.0, 50.0, 'over')); // Equal is not over
    }

    public function test_is_win_under(): void
    {
        $this->assertTrue($this->diceGameService->isWin(25.0, 50.0, 'under'));
        $this->assertFalse($this->diceGameService->isWin(75.0, 50.0, 'under'));
        $this->assertFalse($this->diceGameService->isWin(50.0, 50.0, 'under')); // Equal is not under
    }

    public function test_validate_bet_success(): void
    {
        $errors = $this->diceGameService->validateBet($this->user, $this->currency, 10.0, 50.0, 'over');
        $this->assertEmpty($errors);
    }

    public function test_validate_bet_insufficient_balance(): void
    {
        $errors = $this->diceGameService->validateBet($this->user, $this->currency, 200.0, 50.0, 'over');
        $this->assertContains('Insufficient balance', $errors);
    }

    public function test_validate_bet_invalid_amount(): void
    {
        $errors = $this->diceGameService->validateBet($this->user, $this->currency, 0.0, 50.0, 'over');
        $this->assertContains('Bet amount must be positive', $errors);

        $errors = $this->diceGameService->validateBet($this->user, $this->currency, -10.0, 50.0, 'over');
        $this->assertContains('Bet amount must be positive', $errors);
    }

    public function test_validate_bet_invalid_threshold(): void
    {
        $errors = $this->diceGameService->validateBet($this->user, $this->currency, 10.0, 1.5, 'over');
        $this->assertContains('Threshold must be between 2.00 and 98.00', $errors);

        $errors = $this->diceGameService->validateBet($this->user, $this->currency, 10.0, 98.5, 'over');
        $this->assertContains('Threshold must be between 2.00 and 98.00', $errors);
    }

    public function test_validate_bet_invalid_direction(): void
    {
        $errors = $this->diceGameService->validateBet($this->user, $this->currency, 10.0, 50.0, 'invalid');
        $this->assertContains('Direction must be "over" or "under"', $errors);
    }

    public function test_validate_bet_invalid_win_chance(): void
    {
        // Threshold 98 with direction 'over' gives 2% win chance, which is valid
        $errors = $this->diceGameService->validateBet($this->user, $this->currency, 10.0, 98.0, 'over');
        $this->assertEmpty($errors);

        // Threshold 2 with direction 'under' gives 1% win chance, which is valid
        $errors = $this->diceGameService->validateBet($this->user, $this->currency, 10.0, 2.0, 'under');
        $this->assertEmpty($errors);
    }

    public function test_process_roll_creates_game_record(): void
    {
        $game = $this->diceGameService->processRoll($this->user, $this->currency, 10.0, 50.0, 'over');

        $this->assertNotNull($game);
        $this->assertEquals($this->user->id, $game->user_id);
        $this->assertEquals($this->currency->id, $game->currency_id);
        $this->assertEquals(1000, $game->bet_amount); // $10.00 in cents
        $this->assertEquals(50.0, $game->threshold);
        $this->assertEquals('over', $game->direction);
        $this->assertIsFloat($game->roll_result);
        $this->assertIsBool($game->win);
        $this->assertIsInt($game->profit);
        $this->assertIsFloat($game->multiplier);
        $this->assertEquals(50.0, $game->win_chance);
        $this->assertNotNull($game->reference_id);
    }

    public function test_process_roll_updates_balance(): void
    {
        $initialBalance = $this->balanceService->getBalance($this->user, $this->currency);

        $game = $this->diceGameService->processRoll($this->user, $this->currency, 10.0, 50.0, 'over');

        $finalBalance = $this->balanceService->getBalance($this->user, $this->currency);

        $this->assertEquals($initialBalance, $game->balance_before);
        $this->assertEquals($finalBalance, $game->balance_after);

        if ($game->win) {
            $this->assertGreaterThan($initialBalance, $finalBalance);
        } else {
            $this->assertLessThan($initialBalance, $finalBalance);
        }
    }

    public function test_format_game_result(): void
    {
        $game = $this->diceGameService->processRoll($this->user, $this->currency, 10.0, 50.0, 'over');
        $result = $this->diceGameService->formatGameResult($game);

        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('roll', $result);
        $this->assertArrayHasKey('win', $result);
        $this->assertArrayHasKey('profit', $result);
        $this->assertArrayHasKey('profit_formatted', $result);
        $this->assertArrayHasKey('new_balance', $result);
        $this->assertArrayHasKey('new_balance_formatted', $result);
        $this->assertArrayHasKey('multiplier', $result);
        $this->assertArrayHasKey('win_chance', $result);
        $this->assertArrayHasKey('bet_amount', $result);
        $this->assertArrayHasKey('bet_amount_formatted', $result);
        $this->assertArrayHasKey('threshold', $result);
        $this->assertArrayHasKey('direction', $result);
        $this->assertArrayHasKey('reference_id', $result);
        $this->assertArrayHasKey('created_at', $result);

        $this->assertEquals($game->id, $result['id']);
        $this->assertEquals($game->roll_result, $result['roll']);
        $this->assertEquals($game->win, $result['win']);
        $this->assertEquals(10.0, $result['bet_amount']);
        $this->assertEquals('10.00', $result['bet_amount_formatted']);
    }

    public function test_generate_roll_in_valid_range(): void
    {
        for ($i = 0; $i < 100; $i++) {
            $roll = $this->diceGameService->generateRoll();
            $this->assertGreaterThanOrEqual(0.0, $roll);
            $this->assertLessThan(100.0, $roll);
        }
    }

    public function test_calculate_minimum_bet_usd(): void
    {
        // USD has 2 decimals, 1% house edge
        // Formula: (1 / 10^2) / (1 / 100) = 0.01 / 0.01 = 1.0
        $minBet = $this->diceGameService->calculateMinimumBet($this->currency);
        $this->assertEquals(1.0, $minBet);
    }

    public function test_calculate_minimum_bet_points(): void
    {
        // Create POINTS currency with 0 decimals
        $pointsCurrency = Currency::create([
            'code' => 'POINTS',
            'decimals' => 0,
            'symbol' => 'PTS',
        ]);

        // POINTS has 0 decimals, 1% house edge
        // Formula: (1 / 10^0) / (1 / 100) = 1 / 0.01 = 100
        $minBet = $this->diceGameService->calculateMinimumBet($pointsCurrency);
        $this->assertEquals(100.0, $minBet);
    }

    public function test_validate_bet_below_minimum(): void
    {
        // Create POINTS currency
        $pointsCurrency = Currency::create([
            'code' => 'POINTS',
            'decimals' => 0,
            'symbol' => 'PTS',
        ]);

        // Add POINTS balance
        $this->balanceService->credit($this->user, $pointsCurrency, 1000, 'test_deposit');

        // Try to bet below minimum (100 POINTS)
        $errors = $this->diceGameService->validateBet($this->user, $pointsCurrency, 50.0, 50.0, 'over');
        $this->assertContains('Minimum bet is 100 POINTS to preserve house edge', $errors);
    }

    public function test_validate_bet_at_minimum(): void
    {
        // Create POINTS currency
        $pointsCurrency = Currency::create([
            'code' => 'POINTS',
            'decimals' => 0,
            'symbol' => 'PTS',
        ]);

        // Add POINTS balance
        $this->balanceService->credit($this->user, $pointsCurrency, 1000, 'test_deposit');

        // Bet exactly at minimum (100 POINTS)
        $errors = $this->diceGameService->validateBet($this->user, $pointsCurrency, 100.0, 50.0, 'over');
        $this->assertEmpty($errors);
    }

    public function test_get_minimum_bet_info(): void
    {
        $info = $this->diceGameService->getMinimumBetInfo($this->currency);

        $this->assertArrayHasKey('minimum_bet', $info);
        $this->assertArrayHasKey('minimum_bet_formatted', $info);
        $this->assertArrayHasKey('currency_code', $info);
        $this->assertArrayHasKey('currency_decimals', $info);
        $this->assertArrayHasKey('house_edge', $info);

        $this->assertEquals(1.0, $info['minimum_bet']);
        $this->assertEquals('1.00', $info['minimum_bet_formatted']);
        $this->assertEquals('USD', $info['currency_code']);
        $this->assertEquals(2, $info['currency_decimals']);
        $this->assertEquals(1.0, $info['house_edge']);
    }
}
