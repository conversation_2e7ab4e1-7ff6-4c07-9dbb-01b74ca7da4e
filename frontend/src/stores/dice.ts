import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import websocketService from '@/services/websocket'
import { useAuthStore } from '@/stores/auth'

interface DiceRollRequest {
  bet: number
  threshold: number
  direction: 'over' | 'under'
  currency_id?: number
}

interface DiceRollResult {
  id: number
  roll: number
  win: boolean
  profit: number
  profit_formatted: string
  new_balance: number
  new_balance_formatted: string
  multiplier: number
  win_chance: number
  bet_amount: number
  bet_amount_formatted: string
  threshold: number
  direction: 'over' | 'under'
  reference_id: string
  created_at: string
}

interface DiceCalculateRequest {
  threshold: number
  direction: 'over' | 'under'
}

interface DiceCalculateResult {
  win_chance: number
  multiplier: number
}

export const useDiceStore = defineStore('dice', () => {
  const lastResult = ref<DiceRollResult | null>(null)
  const gameHistory = ref<DiceRollResult[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Statistics
  const totalGames = ref(0)
  const totalWins = ref(0)
  const totalProfit = ref(0)

  const winRate = computed(() => {
    if (totalGames.value === 0) return 0
    return (totalWins.value / totalGames.value) * 100
  })

  // WebSocket callback reference for proper cleanup
  const diceResultCallback = (data: DiceRollResult) => {
    console.log('Dice game result received via WebSocket:', data)

    // Update state with WebSocket result
    lastResult.value = data
    gameHistory.value.unshift(data)

    // Keep only last 50 games in history
    if (gameHistory.value.length > 50) {
      gameHistory.value = gameHistory.value.slice(0, 50)
    }

    // Update statistics
    totalGames.value++
    if (data.win) {
      totalWins.value++
    }
    totalProfit.value += data.profit
  }

  /**
   * Roll the dice
   */
  const roll = async (request: DiceRollRequest): Promise<DiceRollResult> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await axios.post('/api/dice/roll', request)

      if (response.data.success) {
        const result = response.data.data as DiceRollResult

        // Update state
        lastResult.value = result
        gameHistory.value.unshift(result)

        // Keep only last 50 games in history
        if (gameHistory.value.length > 50) {
          gameHistory.value = gameHistory.value.slice(0, 50)
        }

        // Update statistics
        totalGames.value++
        if (result.win) {
          totalWins.value++
        }
        totalProfit.value += result.profit

        return result
      } else {
        throw new Error(response.data.error || 'Roll failed')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'An error occurred'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Calculate win chance and multiplier for given parameters
   */
  const calculate = async (request: DiceCalculateRequest): Promise<DiceCalculateResult> => {
    try {
      const response = await axios.post('/api/dice/calculate', request)

      if (response.data.success) {
        return response.data.data as DiceCalculateResult
      } else {
        throw new Error(response.data.error || 'Calculation failed')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Calculation failed'
      throw new Error(errorMessage)
    }
  }

  /**
   * Clear game history
   */
  const clearHistory = () => {
    gameHistory.value = []
    lastResult.value = null
  }

  /**
   * Reset statistics
   */
  const resetStats = () => {
    totalGames.value = 0
    totalWins.value = 0
    totalProfit.value = 0
  }

  /**
   * Clear error
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * Initialize WebSocket connection for dice game results
   */
  const initializeWebSocket = () => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated || !authStore.user) {
      return
    }

    try {
      // Subscribe to dice game results through the centralized WebSocket service
      websocketService.subscribe('dice.result', diceResultCallback)
      console.log('Dice store: WebSocket initialized')
    } catch (error) {
      console.error('Failed to initialize dice WebSocket:', error)
    }
  }

  /**
   * Clean up WebSocket connection
   */
  const cleanupWebSocket = () => {
    try {
      // Unsubscribe from dice game results
      websocketService.unsubscribe('dice.result', diceResultCallback)
      console.log('Dice store: WebSocket cleaned up')
    } catch (error) {
      console.error('Failed to cleanup dice WebSocket:', error)
    }
  }

  /**
   * Initialize the store
   */
  const initialize = () => {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      cleanupWebSocket()
      return
    }

    initializeWebSocket()
  }

  return {
    // State
    lastResult,
    gameHistory,
    isLoading,
    error,
    totalGames,
    totalWins,
    totalProfit,

    // Computed
    winRate,

    // Actions
    roll,
    calculate,
    clearHistory,
    resetStats,
    clearError,
    initialize,
    cleanupWebSocket,
  }
})
