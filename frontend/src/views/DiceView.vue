<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useBalanceStore } from '@/stores/balance'
import { useDiceStore } from '@/stores/dice'

const authStore = useAuthStore()
const balanceStore = useBalanceStore()
const diceStore = useDiceStore()

// Game state
const betAmount = ref<number>(1.00)
const threshold = ref<number>(50.00)
const direction = ref<'over' | 'under'>('over')
const isRolling = ref<boolean>(false)

// Computed values
const winChance = computed(() => {
  if (direction.value === 'over') {
    return 100.0 - threshold.value
  } else {
    return threshold.value - 1.0
  }
})

const multiplier = computed(() => {
  if (winChance.value <= 0) return 0
  return (100.0 - 1.0) / winChance.value // 1% house edge
})

const estimatedProfit = computed(() => {
  return betAmount.value * multiplier.value - betAmount.value
})

const maxBet = computed(() => {
  if (!balanceStore.currentBalance) return 0
  return balanceStore.currentBalance.formatted
})

const canRoll = computed(() => {
  return betAmount.value > 0 &&
         betAmount.value <= maxBet.value &&
         threshold.value >= 1.00 &&
         threshold.value <= 99.00 &&
         winChance.value > 0 &&
         !isRolling.value
})



// Methods
const toggleDirection = () => {
  direction.value = direction.value === 'over' ? 'under' : 'over'
}

const roll = async () => {
  if (!canRoll.value) return

  isRolling.value = true

  try {
    // Get currency ID from the currencies API
    let currencyId = null
    if (balanceStore.currentBalance) {
      const currenciesResponse = await fetch(`${import.meta.env.VITE_API_URL}/api/currencies`)
      const currenciesData = await currenciesResponse.json()

      if (currenciesData.success) {
        const currency = currenciesData.data.find((c: any) => c.code === balanceStore.currentBalance?.currency_code)
        currencyId = currency?.id
      }
    }

    await diceStore.roll({
      bet: betAmount.value,
      threshold: threshold.value,
      direction: direction.value,
      currency_id: currencyId
    })
  } catch (error) {
    console.error('Roll failed:', error)
  } finally {
    isRolling.value = false
  }
}

const setMaxBet = () => {
  betAmount.value = maxBet.value
}

// Watch for threshold changes to ensure valid win chance
watch([threshold, direction], () => {
  if (winChance.value <= 0) {
    if (direction.value === 'over') {
      threshold.value = 98.99
    } else {
      threshold.value = 2.01
    }
  }
})



onMounted(async () => {
  // Initialize balance store if needed
  if (!balanceStore.isInitialized) {
    await balanceStore.fetchBalances()
  }

  // If still no current balance, try to initialize again
  if (!balanceStore.currentBalance && authStore.isAuthenticated) {
    await balanceStore.initialize()
  }

  // Initialize dice store WebSocket
  diceStore.initialize()
})
</script>

<template>
  <div class="dice-game">
    <div class="game-container">
      <h1 class="game-title">Dice Game</h1>
      <p class="game-description">
        Bet on whether the roll (0-100) will be over or under your chosen threshold.
      </p>



      <div class="game-content">
        <!-- Game Controls -->
        <div class="controls-section">
          <div class="control-group">
            <label for="bet-amount">Bet Amount</label>
            <div class="input-with-max">
              <input
                id="bet-amount"
                v-model.number="betAmount"
                type="number"
                :min="0.01"
                :max="maxBet"
                step="0.01"
                class="bet-input"
                :disabled="isRolling"
              />
              <button @click="setMaxBet" class="max-btn" :disabled="isRolling">
                MAX
              </button>
            </div>
            <div class="balance-info">
              Balance: {{ balanceStore.currentBalance?.formatted || '0.00' }}
              {{ balanceStore.currentBalance?.currency_code || 'USD' }}
            </div>
          </div>

          <div class="control-group">
            <label>Direction</label>
            <div class="direction-toggle">
              <button
                @click="toggleDirection"
                :class="{ active: direction === 'over' }"
                class="direction-btn over-btn"
                :disabled="isRolling"
              >
                Over
              </button>
              <button
                @click="toggleDirection"
                :class="{ active: direction === 'under' }"
                class="direction-btn under-btn"
                :disabled="isRolling"
              >
                Under
              </button>
            </div>
          </div>

          <div class="control-group">
            <label for="threshold">Threshold: {{ threshold.toFixed(2) }}</label>
            <input
              id="threshold"
              v-model.number="threshold"
              type="range"
              min="1.00"
              max="99.00"
              step="0.01"
              class="threshold-slider"
              :disabled="isRolling"
            />
            <div class="threshold-range">
              <span>1.00</span>
              <span>99.00</span>
            </div>
          </div>
        </div>

        <!-- Game Stats -->
        <div class="stats-section">
          <div class="stat-item">
            <span class="stat-label">Win Chance</span>
            <span class="stat-value">{{ winChance.toFixed(2) }}%</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Multiplier</span>
            <span class="stat-value">{{ multiplier.toFixed(4) }}x</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Estimated Profit</span>
            <span class="stat-value" :class="{ positive: estimatedProfit > 0, negative: estimatedProfit < 0 }">
              {{ estimatedProfit >= 0 ? '+' : '' }}{{ estimatedProfit.toFixed(2) }}
            </span>
          </div>
        </div>

        <!-- Roll Button -->
        <div class="roll-section">
          <button
            @click="roll"
            :disabled="!canRoll"
            class="roll-btn"
            :class="{ rolling: isRolling }"
          >
            <span v-if="isRolling">Rolling...</span>
            <span v-else>Roll Dice</span>
          </button>
        </div>

        <!-- Game Result -->
        <div v-if="diceStore.lastResult" class="result-section">
          <div class="result-card" :class="{ win: diceStore.lastResult.win, loss: !diceStore.lastResult.win }">
            <div class="roll-result">
              <span class="roll-label">Roll:</span>
              <span class="roll-value">{{ diceStore.lastResult.roll }}</span>
            </div>
            <div class="outcome">
              <span v-if="diceStore.lastResult.win" class="win-text">You Won!</span>
              <span v-else class="loss-text">You Lost</span>
            </div>
            <div class="profit">
              <span class="profit-label">Profit:</span>
              <span class="profit-value" :class="{ positive: diceStore.lastResult.profit > 0, negative: diceStore.lastResult.profit < 0 }">
                {{ diceStore.lastResult.profit >= 0 ? '+' : '' }}{{ diceStore.lastResult.profit_formatted }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dice-game {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.game-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.game-title {
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.game-description {
  text-align: center;
  color: #6b7280;
  margin-bottom: 2rem;
}

.game-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.controls-section {
  display: grid;
  gap: 1.5rem;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-group label {
  font-weight: 600;
  color: #374151;
}

.input-with-max {
  display: flex;
  gap: 0.5rem;
}

.bet-input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
}

.bet-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.max-btn {
  padding: 0.75rem 1rem;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
}

.max-btn:hover:not(:disabled) {
  background: #4b5563;
}

.balance-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.direction-toggle {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #d1d5db;
}

.direction-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  background: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.direction-btn.active.over-btn {
  background: #10b981;
  color: white;
}

.direction-btn.active.under-btn {
  background: #ef4444;
  color: white;
}

.threshold-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #d1d5db;
  outline: none;
  cursor: pointer;
}

.threshold-range {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #6b7280;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
}

.stat-value.positive {
  color: #10b981;
}

.stat-value.negative {
  color: #ef4444;
}

.roll-section {
  display: flex;
  justify-content: center;
}

.roll-btn {
  padding: 1rem 3rem;
  font-size: 1.25rem;
  font-weight: bold;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 200px;
}

.roll-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-2px);
}

.roll-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.roll-btn.rolling {
  background: #f59e0b;
}

.result-section {
  display: flex;
  justify-content: center;
}

.result-card {
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  min-width: 300px;
}

.result-card.win {
  background: #d1fae5;
  border: 2px solid #10b981;
}

.result-card.loss {
  background: #fee2e2;
  border: 2px solid #ef4444;
}

.roll-result {
  margin-bottom: 1rem;
}

.roll-label {
  font-size: 1.125rem;
  color: #6b7280;
}

.roll-value {
  font-size: 2rem;
  font-weight: bold;
  margin-left: 0.5rem;
  color: #1f2937;
}

.outcome {
  margin-bottom: 1rem;
}

.win-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #10b981;
}

.loss-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ef4444;
}

.profit {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.profit-label {
  font-size: 1.125rem;
  color: #6b7280;
}

.profit-value {
  font-size: 1.25rem;
  font-weight: bold;
}

.profit-value.positive {
  color: #10b981;
}

.profit-value.negative {
  color: #ef4444;
}

@media (max-width: 768px) {
  .dice-game {
    padding: 1rem;
  }

  .game-container {
    padding: 1.5rem;
  }

  .stats-section {
    grid-template-columns: 1fr;
  }

  .roll-btn {
    padding: 0.875rem 2rem;
    font-size: 1.125rem;
    min-width: 180px;
  }
}
</style>
